<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(333,widget1) // 不要删除
import { commonStore } from '~/stores/commonStore'
import { nextTick } from 'vue'
const store = commonStore()
const { pairInfo, isPairDetail, pair, ticker, klineTicker } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  }
})
import { ResolutionManager } from '~/composables/useDatafeedAction'
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
const widgetOption = {
  debug: false,
  symbol: props.pair || pair.value,
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: props.resolution ? ResolutionManager.getResolution(props.resolution) : '15',
  locale: tradingviewLangMap[locale.value] || locale.value,
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null

const Loading = ref(true)
// 添加标志位来跟踪是否是首次初始化
let isFirstInit = true
let currentSymbol = ''

const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }

  // 如果是同一个币种，不需要重新初始化
  if (currentSymbol === currentPair && widget) {
    Loading.value = false
    return
  }

  currentSymbol = currentPair

  // 如果已有 widget 实例且不是首次初始化，尝试复用
  if (widget && !isFirstInit) {
    try {
      // 先检查 widget 是否已经 ready
      if (widget._ready) {
        const chart = widget.activeChart()
        // 检查 setSymbol 方法是否存在
        if (chart && typeof chart.setSymbol === 'function') {
          // 使用 Promise 包装，添加超时控制
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('setSymbol timeout'))
            }, 3000)
            
            try {
              chart.setSymbol(currentPair, () => {
                clearTimeout(timeout)
                Loading.value = false
                resolve(true)
              })
            } catch (err) {
              clearTimeout(timeout)
              reject(err)
            }
          })
          return // 成功切换，直接返回
        }
      } else {
        // widget 未就绪，等待就绪后再尝试
        await new Promise((resolve) => {
          widget.onChartReady(() => {
            try {
              const chart = widget.activeChart()
              if (chart && typeof chart.setSymbol === 'function') {
                chart.setSymbol(currentPair, () => {
                  Loading.value = false
                  resolve(true)
                })
              } else {
                resolve(false)
              }
            } catch (e) {
              resolve(false)
            }
          })
        })
        return
      }
    } catch (e) {
      console.warn('Symbol switch failed, will recreate widget:', e)
      // 如果切换失败，继续执行下面的重建逻辑
    }
  }

  // 首次初始化或复用失败时才销毁重建
  if (widget) {
    try {
      widget.remove()
      widget = null
    } catch (e) {
      console.warn('Widget removal error:', e)
    }
  }

  // 清理旧的datafeed实例
  if (datafeedInstance) {
    try {
      if (datafeedInstance.forceReset) {
        datafeedInstance.forceReset()
      }
      datafeedInstance = null
    } catch (e) {
      console.warn('Datafeed cleanup error:', e)
    }
  }

  // 强制创建新的datafeed实例（每次都是全新的）
  datafeedInstance = useDatafeedAction(pairInfo.value, true)
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? ResolutionManager.getResolution(props.resolution) : '15'
  
  widget = new window.TradingView.widget(widgetOption)
  
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
    isFirstInit = false
  })
}

const previousResolution = ref(props.resolution)
const isTransitioning = ref(false)
let currentTransitionId = 0 // 用于识别当前有效的切换操作
let activeTimeouts = new Set() // 跟踪活跃的timeout

watch(() => props.resolution, async (newVal, oldVal) => {
  // 防抖处理
  if (isTransitioning.value) {
    return
  }
  
  currentTransitionId++
  const thisTransitionId = currentTransitionId
  
  // 清理旧的timeout但保留当前操作
  const oldTimeouts = Array.from(activeTimeouts)
  activeTimeouts.clear()
  setTimeout(() => {
    oldTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
  }, 100)
  
  if (newVal && widget) {
    isTransitioning.value = true
    
    // 设置超时保护，防止isTransitioning永远为true
    const transitionTimeout = setTimeout(() => {
      // 只有当前操作才能重置状态
      if (isTransitioning.value && currentTransitionId === thisTransitionId) {
        isTransitioning.value = false
        Loading.value = false
      }
    }, 4000)
    activeTimeouts.add(transitionTimeout)
    
    // 尝试使用TradingView原生API切换周期，避免重新创建widget
    const chart = widget.activeChart()
    const targetResolution = ResolutionManager.getResolution(newVal)
    
    // 检查是否从1M周期切换出去，如果是则先清理1M数据避免闪烁
    const oldResolution = ResolutionManager.getResolution(oldVal || previousResolution.value)
    const isFrom1M = oldResolution === '1M'
    const isTo1M = targetResolution === '1M'
    
    try {
      
      const currentSymbol = widgetOption.symbol || props.pair || pair.value
      
      // 如果从1M切换到其他周期，使用视觉遮罩避免闪烁
      if (isFrom1M && !isTo1M) {
        try {
          // 暂时隐藏图表内容，避免数据切换时的视觉闪烁
          const klineContainer = document.getElementById('kline_container')
          if (klineContainer) {
            klineContainer.style.visibility = 'hidden'
          }
          
          // 清理1M相关的缓存数据
          const currentPair = currentSymbol
          if (datafeedInstance && datafeedInstance.clearCache) {
            // 清理当前1M的缓存，但保留新周期可能需要的数据
            datafeedInstance.clearCache(true, currentPair, oldVal)
          }
        } catch (error) {
          // 清理失败也不影响后续流程
        }
      }
      
      // 先尝试使用setSymbol方法，避免widget重新创建
      if (widget) {
        // 等待图表完全就绪
        const tryNativeAPI = async () => {
          try {
            // 对于非1M切换，不显示Loading层；对于1M相关切换，已经在外层设置了Loading
            // Loading.value = true
            
            let resolutionChanged = false
            
            // 首先尝试使用setSymbol (如果可用)
            if (typeof widget.setSymbol === 'function') {
              
              // 如果从1M切换出去，给一个短暂延迟确保缓存清理生效
              if (isFrom1M && !isTo1M) {
                await new Promise(resolve => setTimeout(resolve, 50))
              }
              
              // 根据时间周期设置不同的超时时间，1M周期需要更长时间处理数据
              const timeoutDuration = targetResolution === '1M' ? 8000 : 4000
              
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  if (!resolutionChanged) {
                    reject(new Error('setSymbol timeout'))
                  }
                }, timeoutDuration)
                
                widget.setSymbol(currentSymbol, targetResolution, () => {
                  if (currentTransitionId === thisTransitionId) {
                    resolutionChanged = true
                    clearTimeout(timeout)
                    activeTimeouts.delete(transitionTimeout)
                    clearTimeout(transitionTimeout)
                    Loading.value = false
                    isTransitioning.value = false
                    widgetOption.interval = targetResolution
                    
                    // 如果从1M切换出来，恢复图表可见性
                    if (isFrom1M && !isTo1M) {
                      setTimeout(() => {
                        const klineContainer = document.getElementById('kline_container')
                        if (klineContainer) {
                          klineContainer.style.visibility = 'visible'
                        }
                      }, 100) // 给一点延迟确保新数据渲染完成
                    }
                    
                    if (window.updateKlineDataTimestamp) {
                      window.updateKlineDataTimestamp()
                    }
                    
                    resolve(true)
                  }
                })
              })
              
              return true
            } else {
            }
            
            // 如果setSymbol不可用，尝试使用chart.setResolution
            if (chart && typeof chart.setResolution === 'function') {
              
              // 如果从1M切换出去，给一个短暂延迟确保缓存清理生效
              if (isFrom1M && !isTo1M) {
                await new Promise(resolve => setTimeout(resolve, 50))
              }
              
              // 根据时间周期设置不同的超时时间，1M周期需要更长时间处理数据
              const timeoutDuration = targetResolution === '1M' ? 8000 : 4000
              
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  if (!resolutionChanged) {
                    reject(new Error('setResolution timeout'))
                  }
                }, timeoutDuration)
                
                chart.setResolution(targetResolution, () => {
                  if (currentTransitionId === thisTransitionId) {
                    resolutionChanged = true
                    clearTimeout(timeout)
                    activeTimeouts.delete(transitionTimeout)
                    clearTimeout(transitionTimeout)
                    Loading.value = false
                    isTransitioning.value = false
                    widgetOption.interval = targetResolution
                    
                    // 如果从1M切换出来，恢复图表可见性
                    if (isFrom1M && !isTo1M) {
                      setTimeout(() => {
                        const klineContainer = document.getElementById('kline_container')
                        if (klineContainer) {
                          klineContainer.style.visibility = 'visible'
                        }
                      }, 100) // 给一点延迟确保新数据渲染完成
                    }
                    
                    if (window.updateKlineDataTimestamp) {
                      window.updateKlineDataTimestamp()
                    }
                    
                    resolve(true)
                  }
                })
              })
              
              return true
            } else {
            }
            
            throw new Error('No native API available')
            
          } catch (error) {
            // 确保即使出错也恢复图表可见性
            if (isFrom1M && !isTo1M) {
              const klineContainer = document.getElementById('kline_container')
              if (klineContainer) {
                klineContainer.style.visibility = 'visible'
              }
            }
            return false
          }
        }
        
        // 尝试使用原生API，增加重试机制提高成功率
        let retryCount = 0
        const maxRetries = 2
        
        while (retryCount <= maxRetries) {
          try {
            const success = await tryNativeAPI()
            if (success) {
              return
            }
          } catch (error) {
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 100))
              retryCount++
            } else {
              break
            }
          }
        }
      }
    } catch (error) {
      // 确保在错误情况下也重置状态（只有当前操作）
      if (currentTransitionId === thisTransitionId) {
        isTransitioning.value = false
        Loading.value = false
        
        // 确保即使出错也恢复图表可见性
        if (isFrom1M && !isTo1M) {
          const klineContainer = document.getElementById('kline_container')
          if (klineContainer) {
            klineContainer.style.visibility = 'visible'
          }
        }
      }
    }
    
    // 检查是否仍然是当前有效操作
    if (currentTransitionId !== thisTransitionId) {
      return // 已被新的操作替代，直接返回
    }
    
    
    // 重要：在重建前先确保状态清理 - 使用币种专用清理
    if (datafeedInstance) {
      if (datafeedInstance.setSymbolForceRefresh && oldVal) {
        // 使用新的币种专用强制刷新
        datafeedInstance.setSymbolForceRefresh(oldVal, newVal)
      } else if (datafeedInstance.forceReset) {
        // 兜底：使用原有的forceReset
        datafeedInstance.forceReset()
      }
    }
    
    // 强制清理旧实例状态，避免请求冲突
    if (datafeedInstance && datafeedInstance.forceReset) {
      datafeedInstance.forceReset()
    }
    const newDatafeedInstance = useDatafeedAction(pairInfo.value, true)
    
    // 预先设置新的widget配置
    const newWidgetOption = {
      ...widgetOption,
      datafeed: newDatafeedInstance,
      symbol: props.pair || pair.value,
      interval: ResolutionManager.getResolution(newVal)
    }
    // 快速切换：先创建新widget再销毁旧widget，减少空档期
    const oldWidget = widget
    
    try {
      widget = new window.TradingView.widget(newWidgetOption)
      
      widget.onChartReady(() => {
        // 确保只有当前有效操作才能更新状态
        if (currentTransitionId !== thisTransitionId) {
          return
        }
        
        // 新widget就绪后再清理旧的，确保界面连续性
        if (oldWidget) {
          try {
            oldWidget.remove()
          } catch (e) {
          }
        }
        
        // 更新全局引用
        datafeedInstance = newDatafeedInstance
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = newWidgetOption.symbol
        widgetOption.interval = newWidgetOption.interval
        
        widget.activeChart().setChartType(1)
        widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
        
        activeTimeouts.delete(transitionTimeout)
        clearTimeout(transitionTimeout)
        Loading.value = false
        isTransitioning.value = false
        
        // 如果从1M切换出来且使用了重建机制，也要恢复图表可见性
        if (isFrom1M && !isTo1M) {
          const klineContainer = document.getElementById('kline_container')
          if (klineContainer) {
            klineContainer.style.visibility = 'visible'
          }
        }
      })
    } catch (error) {
      if (currentTransitionId === thisTransitionId) {
        activeTimeouts.delete(transitionTimeout)
        clearTimeout(transitionTimeout)
        Loading.value = false
        isTransitioning.value = false
        // 保留旧widget
        widget = oldWidget
        
        // 如果从1M切换出来且重建失败，也要恢复图表可见性
        if (isFrom1M && !isTo1M) {
          const klineContainer = document.getElementById('kline_container')
          if (klineContainer) {
            klineContainer.style.visibility = 'visible'
          }
        }
      }
    }
  }

  previousResolution.value = oldVal
}, { flush: 'post' }) // 使用post确保DOM更新后执行
const currentPairSymbol = computed(() => props.pair || pair.value)
const hasChanged = ref(false)
const lastProcessedSymbol = ref('')
const lastSymbolChangeTime = ref(0)

watch(() => currentPairSymbol.value, async (newVal, oldVal) => {
  const now = Date.now()
  
  if (newVal !== undefined && widget && !isTransitioning.value) {
    if (newVal === lastProcessedSymbol.value && (now - lastSymbolChangeTime.value < 500)) {
      return
    }
    
    if (newVal === oldVal) {
      return
    }
    
    currentTransitionId++
    const thisTransitionId = currentTransitionId
    
    activeTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
    activeTimeouts.clear()
    
    if (isTransitioning.value) {
      isTransitioning.value = false
      Loading.value = false
    }
    
    lastProcessedSymbol.value = newVal
    lastSymbolChangeTime.value = now
    isTransitioning.value = true
    
    const transitionTimeout = setTimeout(() => {
      if (isTransitioning.value && currentTransitionId === thisTransitionId) {
        isTransitioning.value = false
        Loading.value = false
      }
    }, 8000)
    activeTimeouts.add(transitionTimeout)
    
    try {
      const chart = widget.activeChart()
      const targetResolution = props.resolution ? ResolutionManager.getResolution(props.resolution) : '15'
      
      if (widget) {
        const tryNativeAPI = async () => {
          try {
            let symbolChanged = false
            
            if (typeof widget.setSymbol === 'function') {
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                  if (!symbolChanged) {
                    reject(new Error('setSymbol timeout'))
                  }
                }, 3000)
                
                widget.setSymbol(newVal, targetResolution, () => {
                  if (currentTransitionId === thisTransitionId) {
                    symbolChanged = true
                    clearTimeout(timeout)
                    activeTimeouts.delete(transitionTimeout)
                    clearTimeout(transitionTimeout)
                    Loading.value = false
                    isTransitioning.value = false
                    widgetOption.symbol = newVal
                    widgetOption.interval = targetResolution
                    
                    if (window.updateKlineDataTimestamp) {
                      window.updateKlineDataTimestamp()
                    }
                    
                    resolve(true)
                  }
                })
              })
              
              return true
            }
            
            throw new Error('No native API available')
            
          } catch (error) {
            return false
          }
        }
        
        try {
          const success = await tryNativeAPI()
          if (success) {
            return
          }
        } catch (error) {
        }
      }
    } catch (error) {
      if (currentTransitionId === thisTransitionId) {
        isTransitioning.value = false
        Loading.value = false
      }
    }
    
    if (currentTransitionId !== thisTransitionId) {
      return
    }
    
    if (datafeedInstance) {
      if (datafeedInstance.clearSymbolCache) {
        datafeedInstance.clearSymbolCache(oldVal || '', newVal)
      } else if (datafeedInstance.forceReset) {
        datafeedInstance.forceReset()
      }
    }
    
    if (datafeedInstance && datafeedInstance.forceReset) {
      datafeedInstance.forceReset()
    }
    const newDatafeedInstance = useDatafeedAction(pairInfo.value, true)
    
    const newWidgetOption = {
      ...widgetOption,
      datafeed: newDatafeedInstance,
      symbol: newVal,
      interval: props.resolution ? ResolutionManager.getResolution(props.resolution) : '15'
    }
    
    const oldWidget = widget
    
    try {
      widget = new window.TradingView.widget(newWidgetOption)
      
      widget.onChartReady(() => {
        if (currentTransitionId !== thisTransitionId) {
          return
        }
        
        if (oldWidget) {
          try {
            oldWidget.remove()
          } catch (e) {
          }
        }
        
        datafeedInstance = newDatafeedInstance
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = newVal
        widgetOption.interval = newWidgetOption.interval
        
        widget.activeChart().setChartType(1)
        widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
        
        activeTimeouts.delete(transitionTimeout)
        clearTimeout(transitionTimeout)
        Loading.value = false
        isTransitioning.value = false
      })
    } catch (error) {
      if (currentTransitionId === thisTransitionId) {
        activeTimeouts.delete(transitionTimeout)
        clearTimeout(transitionTimeout)
        Loading.value = false
        isTransitioning.value = false
        widget = oldWidget
      }
    }
  }
}, { flush: 'post' }) // 使用post确保DOM更新后执行

onMounted(() => {
  const currentPair = props.pair || pair.value
  
  if (currentPair) {
    nextTick(() => {
      setTimeout(async () => {
        await initChart()
        hasChanged.value = true
      }, 150)
    })
  }
})

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value
  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })

// 添加组件销毁时的清理逻辑
onUnmounted(() => {
  // 清理所有活跃的timeout
  activeTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
  activeTimeouts.clear()
  
  // 销毁TradingView widget
  if (widget) {
    try {
      widget.remove()
      widget = null
    } catch (e) {
      console.warn('TradingView widget cleanup error:', e)
    }
  }
  
  // 清理datafeed实例
  if (datafeedInstance) {
    try {
      if (datafeedInstance.forceReset) {
        datafeedInstance.forceReset()
      }
      if (datafeedInstance.cleanup) {
        datafeedInstance.cleanup()
      }
      datafeedInstance = null
    } catch (e) {
      console.warn('Datafeed cleanup error:', e)
    }
  }
  
  // 重置状态
  Loading.value = true
  isTransitioning.value = false
  currentTransitionId = 0
  
  // 清理DOM
  const klineContainer = document.getElementById('kline_container')
  if (klineContainer) {
    klineContainer.innerHTML = ''
    klineContainer.style.visibility = 'visible'
  }
})
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>