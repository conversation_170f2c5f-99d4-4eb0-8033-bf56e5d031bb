<template>
  <div class="exchnage-deals-wrap">
    <div class="deals-row fit-tc-primary head">
      <div class="deals-row__item left">{{ $t('时间') }}</div>
      <div class="deals-row__item right">{{ $t('价格') }}</div>
      <div class="deals-row__item right">{{ $t('数量') }}</div>
    </div>
    <el-scrollbar wrap-class="deals-area" tag="ul">
      <li v-for="(item, index) in filterDeals" :key="index">
        <span class="left">{{ timeFormat(item.t, 'hh:mm:ss') }}</span>
        <span class="right" :class="Number(item.s) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(item.p, priceScale, true) }}</span>
        <span class="right" :class="Number(item.s) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format((Number(item.q) < 0 ? -Number(item.q) : item.q), quantityScale, true) }}</span>
      </li>
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
  import { timeFormat, format } from '~/utils'
  import { ElScrollbar } from 'element-plus'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { dealsObj } = storeToRefs(store)
  const props = defineProps({
    deals: {
      type: Object,
      default () {
        return []
      }
    },
    pair: {
      type: String,
      default: ''
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    priceScale: {
      type: [Number, String],
      default: ''
    },
    quantityScale: {
      type: [Number, String],
      default: ''
    }
  })
  const deals = ref({})
  const filterDeals = computed(() => {
    const pairDeals = deals.value[props.pair]
    if (!pairDeals || typeof pairDeals !== 'object') {
      return []
    }
    return Object.values(pairDeals).reverse()
  })
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    if (dealsObj.value) {
      deals.value = dealsObj.value
    }
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss">
@import url('@/assets/style/exchange/deals.scss');
</style>
