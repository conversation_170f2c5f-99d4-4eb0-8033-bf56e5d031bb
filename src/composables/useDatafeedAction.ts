import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export class ResolutionManager {
  static readonly RESOLUTION_MAP: any = {
    1: '1m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    '1W': '1w',
    '1M': '1M'
  }

  static readonly RESOLUTION_RE_MAP: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }

  static isMonthly(resolution: string): boolean {
    return resolution === '1M'
  }

  static getInterval(resolution: string): string {
    return this.RESOLUTION_MAP[resolution] || resolution
  }

  static getResolution(interval: string): string {
    return this.RESOLUTION_RE_MAP[interval] || interval
  }
}

// 全局datafeed单例存储（改为Map以支持多实例）
const globalDatafeedInstances = new Map()
let instanceCount = 0

// 清理函数，在创建新实例前调用
function cleanupOldInstance(key = 'default') {
  if (globalDatafeedInstances.has(key)) {
    const instance = globalDatafeedInstances.get(key)
    if (instance && instance.forceReset) {
      instance.forceReset()
    }
    if (instance && instance.cleanup) {
      instance.cleanup()
    }
    globalDatafeedInstances.delete(key)
  }
}

// 清理所有实例
function cleanupAllInstances() {
  for (const [key, instance] of globalDatafeedInstances.entries()) {
    if (instance && instance.forceReset) {
      instance.forceReset()
    }
    if (instance && instance.cleanup) {
      instance.cleanup()
    }
  }
  globalDatafeedInstances.clear()
}

export default function useDatafeedAction(info, forceNew = false) {
  const instanceKey = `instance_${instanceCount++}`
  
  // 如果强制创建新实例，清理所有旧实例
  if (forceNew) {
    cleanupAllInstances()
  }
  
  // 为每个新实例生成唯一标识

  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pendingRequests = new Map()
  const REQUEST_TIMEOUT = 30000
  const activeRequests = new Map()
  const REQUEST_DEBOUNCE = 30
  const MAX_CONCURRENT_REQUESTS = 4
  const EMERGENCY_BYPASS_INTERVAL = 2000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker, marketsObj } = storeToRefs(store)

  // 重构订阅管理 - 按照官方文档最佳实践
  const subscriptions = new Map() // subscriberUID -> subscription info
  const symbolSubscriptions = new Map() // symbol_resolution -> Set of subscriberUIDs
  const lastBarsCache = new Map() // symbol -> last bar (官方推荐的缓存机制)
  let rafId: number | null = null

  function formatSymbol(symbol: string) {
    // 确保 symbol 是有效的字符串
    if (!symbol || typeof symbol !== 'string') {
      return ''
    }
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }

  function getCurrentBarStartTime(resolution: string, currentTime: number): number {
    const resolutionMinutes: { [key: string]: number } = {
      '1': 1, '5': 5, '15': 15, '30': 30, '60': 60,
      '120': 120, '240': 240, '360': 360, '480': 480, '720': 720
    }
    
    if (resolution === '1D') {
      const date = new Date(currentTime)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    } else if (resolution === '1W') {
      const date = new Date(currentTime)
      const day = date.getUTCDay()
      date.setUTCDate(date.getUTCDate() - day)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    } else if (resolution === '1M') {
      const date = new Date(currentTime)
      date.setUTCDate(1)
      date.setUTCHours(0, 0, 0, 0)
      return date.getTime()
    }
    
    const minutes = resolutionMinutes[resolution] || 60
    const msPerMinute = 60 * 1000
    const periodMs = minutes * msPerMinute
    return Math.floor(currentTime / periodMs) * periodMs
  }

  function getCurrentPrice(symbol: string): number | null {
    try {
      const formattedSymbol = formatSymbol(symbol)
      
      const currentTicker = ticker.value[formattedSymbol]
      if (currentTicker && currentTicker.last) {
        return safeNumber(currentTicker.last)
      }
      
      const intervalMap = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '1w', '1M']
      for (const testInterval of intervalMap) {
        if (klineTicker.value.currentPair === formattedSymbol && 
            klineTicker.value.currentPeriod === testInterval &&
            klineTicker.value.close) {
          return safeNumber(klineTicker.value.close)
        }
      }
      
      const marketsData = marketsObj.value[formattedSymbol]
      if (marketsData && marketsData.last) {
        return safeNumber(marketsData.last)
      }
      
      return null
    } catch (error) {
      return null
    }
  }
  
  // 新增：监听commonStore中的价格变化，实时更新TradingView
  let lastPriceUpdateTime = new Map() // symbol -> timestamp
  let tickerWatcher: any = null // 存储watcher引用用于清理
  
  // 监听ticker变化，检测价格实时更新
  tickerWatcher = watch(ticker, (newTicker) => {
    if (!newTicker || typeof newTicker !== 'object') return
    
    Object.keys(newTicker).forEach(symbol => {
      const tickerData = newTicker[symbol]
      if (!tickerData || !tickerData.last) return
      
      const currentTime = Date.now()
      const lastUpdateTime = lastPriceUpdateTime.get(symbol) || 0
      
      // 检查是否是来自trade的实时更新（有_syncInfo标记）
      if (tickerData._syncInfo && tickerData._syncInfo.source === 'trade') {
        const timeDiff = currentTime - lastUpdateTime
        
        // 避免过于频繁的更新（最少间隔100ms）
        if (timeDiff > 100) {
          lastPriceUpdateTime.set(symbol, currentTime)
          
          // 推送实时价格更新到TradingView订阅者
          const formattedSymbol = formatSymbol(symbol)
          pushPriceUpdateToTradingView(formattedSymbol, safeNumber(tickerData.last))
        }
      }
    })
  }, { deep: true })
  
  // 新增：推送价格更新到TradingView的函数
  function pushPriceUpdateToTradingView(symbol: string, newPrice: number) {
    if (!symbol || !newPrice || newPrice <= 0) return
    
    // 遍历所有可能的时间周期，更新对应的订阅者
    const intervals = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '1w', '1M']
    
    intervals.forEach(interval => {
      const symbolKey = `${symbol}_${interval}`
      const subscriberIds = symbolSubscriptions.get(symbolKey)
      
      if (subscriberIds && subscriberIds.size > 0) {
        const lastBar = lastBarData.get(symbolKey)
        
        if (lastBar) {
          // 数据验证：确保lastBar包含有效的数值
          const currentHigh = Number(lastBar.high) || newPrice
          const currentLow = Number(lastBar.low) || newPrice
          
          // 更新最后一个bar的价格
          const updatedBar = {
            ...lastBar,
            close: newPrice,
            high: Math.max(currentHigh, newPrice),
            low: Math.min(currentLow, newPrice)
          }
          
          // 更新缓存
          lastBarData.set(symbolKey, updatedBar)
          
          // 推送到订阅者
          subscriberIds.forEach(subscriberUID => {
            const subscription = subscriptions.get(subscriberUID)
            if (subscription && subscription.active) {
              if (formatSymbol(subscription.symbol) === symbol &&
                subscription.interval === interval) {
                try {
                  const barDataCopy = { ...updatedBar }
                  subscription.callback(barDataCopy)
                } catch (error) {
                }
              }
            }
          })
        }
      }
    })
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    // 兼容处理：获取正确的symbol名称
    const symbolName = symbolInfo.full_name || symbolInfo.fullName || symbolInfo.name || symbolInfo.symbol

    if (firstDataRequest && resolution === '1M') {
      if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
        klineTicker.value.currentPair === symbolName) {
        preObj.value = klineList.value[klineList.value.length - 1]
        onHistoryCallback(klineList.value, { noData: klineList.value.length === 0 })
        return true
      }

      const waitForWebSocketData = () => {
        return new Promise((resolve) => {
          let attempts = 0
          const maxAttempts = 20
          const checkInterval = 100

          const checkData = () => {
            attempts++
            if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
              klineTicker.value.currentPair === symbolName) {
              preObj.value = klineList.value[klineList.value.length - 1]
              onHistoryCallback(klineList.value, { noData: klineList.value.length === 0 })
              resolve(true)
            } else if (attempts >= maxAttempts) {
              resolve(false)
            } else {
              setTimeout(checkData, checkInterval)
            }
          }

          setTimeout(checkData, checkInterval)
        })
      }

      const hasWebSocketData = await waitForWebSocketData()
      return hasWebSocketData
    }
    return false
  }

  async function handleWebSocketData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any) {
    const { firstDataRequest } = periodParams
    const symbolName = symbolInfo.full_name || symbolInfo.fullName || symbolInfo.name || symbolInfo.symbol
    const currentInterval = ResolutionManager.getInterval(resolution)
    
    // 改进：更严格的数据验证，确保数据匹配且是最新的
    if (firstDataRequest && 
        klineList.value.length > 0 && 
        klineTicker.value.currentPeriod === currentInterval &&
        klineTicker.value.currentPair === symbolName) {
      
      const formattedData = klineList.value.map(item => ({
        time: item.time || item.timestamp,
        open: safeNumber(item.open),
        high: safeNumber(item.high),
        low: safeNumber(item.low),
        close: safeNumber(item.close),
        volume: safeNumber(item.volume)
      })).filter(item => item.time > 0)
      
      if (formattedData.length > 0) {
        preObj.value = formattedData[formattedData.length - 1]
        
        const currentPrice = getCurrentPrice(symbolName)
        if (currentPrice) {
          const lastBar = formattedData[formattedData.length - 1]
          lastBar.close = currentPrice
          lastBar.high = Math.max(lastBar.high, currentPrice)
          lastBar.low = Math.min(lastBar.low, currentPrice)
        }
        
        lastBarsCache.set(symbolName, formattedData[formattedData.length - 1])
        
        onHistoryCallback(formattedData, { noData: false })
        return true
      }
    }
    
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    // 验证必要的参数
    if (!symbolInfo) {
      onErrorCallback('Invalid symbol info')
      return
    }

    // 兼容处理：优先使用 full_name，其次 fullName，最后 name
    const symbolName = symbolInfo.full_name || symbolInfo.fullName || symbolInfo.name || symbolInfo.symbol
    if (!symbolName) {
      onErrorCallback('No symbol name')
      return
    }

    const requestIdentifier = `${symbolName}-${resolution}`
    const now = Date.now()
    const currentRequests = activeRequests.get(requestIdentifier)
    const { from, to, firstDataRequest, countBack } = periodParams || {}

    if (currentRequests && !firstDataRequest) {
      const timeSinceLastRequest = now - currentRequests.timestamp
      const isEmergencyBypass = timeSinceLastRequest > EMERGENCY_BYPASS_INTERVAL

      if (currentRequests.count >= MAX_CONCURRENT_REQUESTS && !isEmergencyBypass) {
        onErrorCallback('Too many concurrent requests')
        return
      }

      if (timeSinceLastRequest < REQUEST_DEBOUNCE && !isEmergencyBypass) {
        await new Promise(resolve => setTimeout(resolve, REQUEST_DEBOUNCE))
      }
    }

    const requestId = `${requestIdentifier}-${now}-${Math.random().toString(36).substr(2, 9)}`
    if (!firstDataRequest) {
      activeRequests.set(requestIdentifier, {
        timestamp: now,
        count: currentRequests ? currentRequests.count + 1 : 1,
        requestId: requestId
      })
    }

    if (!firstDataRequest) {
      setTimeout(() => {
        const current = activeRequests.get(requestIdentifier)
        if (current && current.requestId === requestId) {
          if (current.count <= 1) {
            activeRequests.delete(requestIdentifier)
          } else {
            activeRequests.set(requestIdentifier, {
              ...current,
              count: current.count - 1
            })
          }
        }
      }, REQUEST_TIMEOUT)
    }

    pair.value = symbolName
    interval.value = ResolutionManager.getInterval(resolution)
    key = `${symbolName}-${resolution}`

    // 在首次请求时重置时间序列，确保时间从干净状态开始
    if (firstDataRequest) {
      const symbolKey = `${symbolName}_${ResolutionManager.getInterval(resolution)}`
      resetTimeSequence(symbolKey)

      // 关键修复：确保不会混用不同周期的数据
      // 清理其他周期的时间记录，避免数据混淆
      const currentInterval = ResolutionManager.getInterval(resolution)
      const keysToRemove = []
      lastBarTime.forEach((value, key) => {
        // 如果是同一个symbol但不同的周期，清理掉
        if (key.startsWith(`${symbolName}_`) && !key.endsWith(`_${currentInterval}`)) {
          keysToRemove.push(key)
        }
      })
      keysToRemove.forEach(key => {
        lastBarTime.delete(key)
        lastBarData.delete(key)
      })
    }

    // 优化：避免不必要的数据清理，保持实时数据流的连续性
    if (firstDataRequest && resolution !== '1M') {
      const monthlyKey = `${symbolName}-1M-first`
      if (dataCache.has(monthlyKey)) {
        dataCache.delete(monthlyKey)
      }
      // 只在确实需要时清理1M数据
      if (klineTicker.value.currentPeriod === '1M' && klineTicker.value.currentPair === symbolName) {
        klineList.value = []
        klineTicker.value = {}
      }
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    if (firstDataRequest) {
      const handled = await handleWebSocketData(symbolInfo, resolution, periodParams, onHistoryCallback)
      if (handled) {
        const cacheKey = `${symbolName}-${resolution}-first`
        dataCache.set(cacheKey, {
          data: lastBarsCache.get(symbolName),
          timestamp: Date.now(),
          symbol: symbolName,
          resolution: resolution
        })
        return
      }
    }

    try {
      await fetchHistoricalData(symbolName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
    } finally {
      if (!firstDataRequest) {
        const currentRequests = activeRequests.get(requestIdentifier)
        if (currentRequests && currentRequests.requestId === requestId) {
          if (currentRequests.count <= 1) {
            activeRequests.delete(requestIdentifier)
          } else {
            activeRequests.set(requestIdentifier, {
              ...currentRequests,
              count: currentRequests.count - 1
            })
          }
        }
      }
    }
  }
  const forceRefresh = ref(false)

  const clearCache = (preserveRealtime: boolean = false, currentSymbol?: string, currentResolution?: string) => {
    if (preserveRealtime && currentSymbol && currentResolution) {
      // 轻量级清理：只清理无关的缓存，保留当前实时数据
      const currentKey = `${currentSymbol}-${ResolutionManager.getResolution(currentResolution)}`
      const preserveKeys = [
        `${currentSymbol}-${ResolutionManager.getResolution(currentResolution)}-first`,
        currentKey
      ]

      // 只删除不相关的缓存项
      const keysToDelete = []
      dataCache.forEach((value, key) => {
        if (!preserveKeys.some(preserveKey => key.includes(preserveKey))) {
          keysToDelete.push(key)
        }
      })
      keysToDelete.forEach(key => dataCache.delete(key))

      // 保留当前订阅的实时数据状态
      const currentSubKey = `${currentSymbol}_#_${ResolutionManager.getInterval(ResolutionManager.getResolution(currentResolution))}`
      Object.keys(lastCompleteBar.value).forEach(key => {
        if (key !== currentSubKey) {
          delete lastCompleteBar.value[key]
        }
      })

      // 清理无关的时间戳记录，保留当前的
      const currentTimeKey = `${currentSymbol}_${ResolutionManager.getInterval(ResolutionManager.getResolution(currentResolution))}`
      const keysToDeleteFromTime = []
      lastBarTime.forEach((value, key) => {
        if (key !== currentTimeKey) {
          keysToDeleteFromTime.push(key)
        }
      })
      keysToDeleteFromTime.forEach(key => {
        lastBarTime.delete(key)
        lastBarData.delete(key) // 同时清理bar数据缓存
      })
    } else {
      // 完全清理（原有逻辑）
      dataCache.clear()
      lastCompleteBar.value = {}
      lastBarTime.clear()
      lastBarData.clear() // 清理新的bar数据缓存
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}
    }
  }

  // 添加专门用于清理时间记录的函数
  const resetTimeSequence = (symbolKey?: string) => {
    if (symbolKey) {
      lastBarTime.delete(symbolKey)
      lastBarData.delete(symbolKey) // 同时清理bar数据
    } else {
      lastBarTime.clear()
      lastBarData.clear() // 同时清理所有bar数据
    }
  }

  const clearSymbolCache = (oldSymbol: string, newSymbol: string) => {
    if (newSymbol && oldSymbol !== newSymbol) {
      const preserveKeys = [
        `${newSymbol}-${ResolutionManager.getResolution(interval.value)}-first`,
        `${newSymbol}-${ResolutionManager.getResolution(interval.value)}`
      ]

      const keysToDelete = []
      dataCache.forEach((value, key) => {
        if (key.startsWith(`${oldSymbol}-`) || (!preserveKeys.some(preserveKey => key.includes(preserveKey)) && key.startsWith(`${newSymbol}-`))) {
          keysToDelete.push(key)
        }
      })
      keysToDelete.forEach(key => dataCache.delete(key))

      const currentSubKey = `${newSymbol}_#_${ResolutionManager.getInterval(interval.value)}`
      Object.keys(lastCompleteBar.value).forEach(key => {
        if (key.includes(oldSymbol) || (key !== currentSubKey && key.includes(newSymbol))) {
          delete lastCompleteBar.value[key]
        }
      })

      // 清理旧币种的时间戳记录，保留新币种的
      const currentTimeKey = `${newSymbol}_${ResolutionManager.getInterval(interval.value)}`
      const keysToDeleteFromTime = []
      lastBarTime.forEach((value, key) => {
        if (key.includes(oldSymbol) || (key !== currentTimeKey && key.includes(newSymbol))) {
          keysToDeleteFromTime.push(key)
        }
      })
      keysToDeleteFromTime.forEach(key => {
        lastBarTime.delete(key)
        lastBarData.delete(key)
      })

      if (monthlySubscriptionCache.pair === oldSymbol) {
        monthlySubscriptionCache = {
          key: null,
          subscription: null,
          pair: null
        }
      }

      if (klineTicker.value.currentPair === oldSymbol) {
        klineList.value = []
        klineTicker.value = {}
      }
    } else {
      dataCache.clear()
      lastCompleteBar.value = {}
      lastBarTime.clear()
      lastBarData.clear()
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}
    }
  }

  const setSymbolForceRefresh = (oldSymbol: string, newSymbol: string) => {
    clearCache(true, newSymbol, interval.value)
    forceRefresh.value = true
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
    }
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const requestKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : `${from}-${to}`}-${countBack}`
    const baseRequestKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : 'historical'}`
    if (pendingRequests.has(requestKey)) {
      try {
        const result = await pendingRequests.get(requestKey)
        onHistoryCallback(result.data, result.noData)
        return
      } catch (error) {
        onErrorCallback(error)
        return
      }
    }

    const cacheKey = `${symbol}-${resolution}-${firstDataRequest ? 'first' : `${from}-${to}`}`
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = resolution === '1M'
    const cacheTimeout = firstDataRequest ? 10 * 60 * 1000 : (isMonthlyResolution ? 60 * 1000 : CACHE_DURATION * 2)

    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    const isFromMonthlyToMinute = hasMonthlyCache && resolution === 1 && firstDataRequest
    const shouldForceRefresh = forceRefresh.value || isFromMonthlyToMinute

    const canUseCache = cachedData &&
      (Date.now() - cachedData.timestamp < cacheTimeout) &&
      !shouldForceRefresh &&
      (firstDataRequest || (!firstDataRequest && cachedData.from === from && cachedData.to === to)) &&
      cachedData.data && cachedData.data.length > 0

    if (canUseCache) {
      onHistoryCallback(cachedData.data, { noData: cachedData.data.length === 0 })
      return
    }

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    const requestPromise = (async () => {
      try {
        const now = Date.now()
        const requestLimit = isMonthlyResolution && firstDataRequest ?
          Math.max(countBack || 500, 100) :
          Math.max(countBack || 500, 200)
        // 确保 symbol 有效
        const formattedSymbol = formatSymbol(symbol)
        if (!formattedSymbol) {
          throw new Error('Invalid symbol')
        }

        const beforeTime = firstDataRequest ? now : preObj.value.time
        const apiParams = {
          symbol: formattedSymbol,
          market: formattedSymbol.includes('_SWAP') ? 'lpc' : 'spot',
          time_frame: ResolutionManager.getInterval(resolution),
          before: beforeTime,
          limit: requestLimit,
          origin: 1,
        }

        // 对于TradingView的范围请求，需要调整参数
        if (!firstDataRequest && from && to) {
          // 使用TradingView请求的确切时间范围
          apiParams.before = to * 1000 // TradingView使用秒，API使用毫秒
        }

        const { data } = await getKlinesApi(apiParams)
        if (data && data.e && Array.isArray(data.e)) {
          let formattedData = data.e.map(item => {
            // 确保所有值都是有效的数字
            const time = Number(item[0]) || Date.now()
            const open = safeNumber(item[1])
            const high = safeNumber(item[2])
            const low = safeNumber(item[3])
            const close = safeNumber(item[4])
            const volume = safeNumber(item[5])

            // 验证数据有效性
            if (isNaN(time) || isNaN(open) || isNaN(close)) {
              return null
            }

            return { time, open, high, low, close, volume }
          }).filter(Boolean).sort((a, b) => a.time - b.time)

          // 对于非首次请求，确保数据在TradingView请求的时间范围内
          if (!firstDataRequest && from && to) {
            const fromMs = from * 1000
            const toMs = to * 1000
            const originalLength = formattedData.length

            formattedData = formattedData.filter(bar => {
              return bar.time >= fromMs && bar.time <= toMs
            })
          }

          const isDailyOrAbove = ['1D', '3D', '1W', '1M'].includes(resolution)
          const increment = isDailyOrAbove ? 24 * 60 * 60 * 1000 : 60000

          for (let i = 1; i < formattedData.length; i++) {
            if (formattedData[i].time <= formattedData[i - 1].time) {
              const previousTime = formattedData[i - 1].time
              formattedData[i].time = previousTime + increment
            }
          }

          if (formattedData.length < 2 && firstDataRequest) {
            const lastBar = formattedData[formattedData.length - 1]
            if (lastBar) {
              const syntheticBar = {
                time: lastBar.time - increment,
                open: lastBar.open,
                high: lastBar.high,
                low: lastBar.low, 
                close: lastBar.open,
                volume: 0
              }
              formattedData.unshift(syntheticBar)
            }
          }

          if (formattedData.length > 0) {
            preObj.value = formattedData[formattedData.length - 1]
            if (firstDataRequest || !firstDataRequest) {
              dataCache.set(cacheKey, {
                data: formattedData,
                timestamp: now,
                from: firstDataRequest ? undefined : from,
                to: firstDataRequest ? undefined : to,
                symbol: symbol,
                resolution: resolution
              })
            }

            if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
              klineList.value = formattedData
              klineTicker.value = {
                ...formattedData[formattedData.length - 1],
                currentPair: symbol,
                currentPeriod: '1M'
              }
            }
          }

          // 关键修复：只在首次请求时设置最后一个bar的时间记录
          // 避免历史数据加载影响实时数据的时间验证
          if (firstDataRequest && formattedData.length > 0) {
            const symbolKey = `${symbol}_${ResolutionManager.getInterval(resolution)}`
            const lastBar = formattedData[formattedData.length - 1]

            // 首次加载时设置基准时间
            lastBarTime.set(symbolKey, lastBar.time)
            lastBarData.set(symbolKey, lastBar)
          }

          // 对于日线数据，需要特殊处理时间
          if (isDailyOrAbove && formattedData.length > 0) {
            // 日线数据的时间应该是当天的开始时间（00:00:00 UTC）
            formattedData.forEach(bar => {
              const date = new Date(bar.time)
              date.setUTCHours(0, 0, 0, 0)
              bar.time = date.getTime()
            })

            // 重新排序并去重（基于时间）
            const uniqueBars = new Map()
            formattedData.forEach(bar => {
              uniqueBars.set(bar.time, bar)
            })
            formattedData = Array.from(uniqueBars.values()).sort((a, b) => a.time - b.time)
          }

          let finalData = formattedData
          if (firstDataRequest && formattedData.length > 0) {
            let currentPrice = getCurrentPrice(symbol)
            
            if (!currentPrice) {
              let retryCount = 0
              while (!currentPrice && retryCount < 3) {
                await new Promise(resolve => setTimeout(resolve, 100))
                currentPrice = getCurrentPrice(symbol)
                retryCount++
              }
            }
            
            const now = Date.now()
            const currentBarStartTime = getCurrentBarStartTime(resolution, now)
            const lastBar = formattedData[formattedData.length - 1]
            
            if (lastBar.time < currentBarStartTime) {
              if (currentPrice) {
                const currentBar = {
                  time: currentBarStartTime,
                  open: currentPrice,
                  high: currentPrice,
                  low: currentPrice,
                  close: currentPrice,
                  volume: 0
                }
                finalData = [...formattedData, currentBar]
                lastBarsCache.set(symbol, currentBar)
                
                const symbolKey = `${symbol}_${ResolutionManager.getInterval(resolution)}`
                lastBarData.set(symbolKey, currentBar)
              } else {
                const lastClose = lastBar.close
                const currentBar = {
                  time: currentBarStartTime,
                  open: lastClose,
                  high: lastClose,
                  low: lastClose,
                  close: lastClose,
                  volume: 0
                }
                finalData = [...formattedData, currentBar]
                lastBarsCache.set(symbol, currentBar)
                
                const symbolKey = `${symbol}_${ResolutionManager.getInterval(resolution)}`
                lastBarData.set(symbolKey, currentBar)
              }
            } else if (currentPrice) {
              const updatedLastBar = {
                ...lastBar,
                close: currentPrice,
                high: Math.max(lastBar.high, currentPrice),
                low: Math.min(lastBar.low, currentPrice)
              }
              finalData = [...formattedData.slice(0, -1), updatedLastBar]
              lastBarsCache.set(symbol, updatedLastBar)
              
              const symbolKey = `${symbol}_${ResolutionManager.getInterval(resolution)}`
              lastBarData.set(symbolKey, updatedLastBar)
            } else {
              lastBarsCache.set(symbol, lastBar)
            }
          } else if (formattedData.length > 0) {
            lastBarsCache.set(symbol, formattedData[formattedData.length - 1])
          }

          // 确保所有bar的数据都是有效的
          const validatedData = finalData.map(bar => ({
            time: Number(bar.time) || 0,
            open: Number(bar.open) || 0,
            high: Number(bar.high) || 0,
            low: Number(bar.low) || 0,
            close: Number(bar.close) || 0,
            volume: Number(bar.volume) || 0
          }))

          // 如果过滤后没有数据，告诉TradingView没有更多数据
          const hasNoData = validatedData.length === 0
          if (hasNoData && !firstDataRequest) {
          }

          return { data: validatedData, noData: { noData: hasNoData } }
        } else {
          throw new Error('No data received from API')
        }
      } catch (error) {
        throw error
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    // 临时移除基础请求键存储
    // pendingRequests.set(baseRequestKey, requestPromise)

    const timeoutId = setTimeout(() => {
      pendingRequests.delete(requestKey)
      // pendingRequests.delete(baseRequestKey)
    }, REQUEST_TIMEOUT)

    try {
      const result = await requestPromise
      onHistoryCallback(result.data, result.noData)
    } catch (error) {
      onErrorCallback(error?.message || error || 'Unknown error')
    } finally {
      pendingRequests.delete(requestKey)
      // pendingRequests.delete(baseRequestKey)
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (!val2 || val2.currentPeriod !== '1M' || !val2.currentPair) {
      return false
    }

    const currentSymbol = formatSymbol(val2.currentPair)
    const symbolKey = `${currentSymbol}_1M`

    // 检查是否有1M周期的订阅者
    const subscriberIds = symbolSubscriptions.get(symbolKey)
    if (!subscriberIds || subscriberIds.size === 0) {
      return false
    }

    const last = (val1[currentSymbol] || {}).last
    if (!last || !val2.time || val2.open === undefined) {
      return false
    }

    // 构建1M K线数据
    const resultVal = {
      time: Number(val2.time),
      open: safeNumber(val2.open),
      high: safeNumber(val2.high),
      low: safeNumber(val2.low),
      close: safeNumber(last),
      volume: safeNumber(val2.volume)
    }

    // 更新缓存
    lastCompleteBar.value[symbolKey] = {
      open: safeNumber(val2.open),
      high: safeNumber(val2.high),
      low: safeNumber(val2.low),
      volume: safeNumber(val2.volume)
    }

    // 向所有1M订阅者推送数据
    let pushCount = 0
    subscriberIds.forEach(subscriberUID => {
      const subscription = subscriptions.get(subscriberUID)
      if (subscription && subscription.active && subscription.interval === '1M') {
        try {
          subscription.callback(resultVal)
          pushCount++
        } catch (error) {
        }
      }
    })
    return true
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    // 通知数据更新时间戳（用于卡死检测）
    if (window.updateKlineDataTimestamp) {
      window.updateKlineDataTimestamp()
    }

    // 处理月度数据更新
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    // 使用新的订阅管理机制推送数据
    pushRealtimeDataToSubscribers(val1, val2)
  }, { deep: true })

  // 数据有效性验证函数
  function validateKlineData(klineData: any, symbolKey: string): { isValid: boolean, reason?: string } {
    if (!klineData || !klineData.time) {
      return { isValid: false, reason: 'Missing time data' }
    }

    const barTime = Number(klineData.time)
    const now = Date.now()
    const lastTime = lastBarTime.get(symbolKey) || 0

    // 对于初始订阅，放宽验证规则
    const isInitialSubscription = lastTime === 0

    if (isInitialSubscription) {
      // 初始订阅时，只验证基本的时间合理性
      // 检查数据不能来自太遥远的未来
      const maxFutureTime = now + (60 * 60 * 1000) // 1小时后
      if (barTime > maxFutureTime) {
        return {
          isValid: false,
          reason: `Data too far in future: ${new Date(barTime).toISOString()}`
        }
      }
      // 初始数据可以是历史数据，不检查过去时间
      return { isValid: true }
    }

    // 已有订阅的严格验证
    // 检查1: 数据不能来自太久的过去（可能是历史数据混入）
    const maxPastTime = now - (24 * 60 * 60 * 1000) // 24小时前
    if (barTime < maxPastTime) {
      return {
        isValid: false,
        reason: `Data too old: ${new Date(barTime).toISOString()} (more than 24h ago)`
      }
    }

    // 检查2: 数据不能来自太遥远的未来
    const maxFutureTime = now + (60 * 60 * 1000) // 1小时后
    if (barTime > maxFutureTime) {
      return {
        isValid: false,
        reason: `Data too far in future: ${new Date(barTime).toISOString()}`
      }
    }

    // 检查3: 如果有历史数据，新数据时间不能早于最后一个bar超过15分钟
    if (lastTime > 0) {
      const timeDiff = lastTime - barTime
      const maxAllowedBacktrack = 15 * 60 * 1000 // 15分钟

      if (timeDiff > maxAllowedBacktrack) {
        return {
          isValid: false,
          reason: `Time violation: new ${new Date(barTime).toISOString()} vs last ${new Date(lastTime).toISOString()} (diff: ${Math.round(timeDiff / 60000)}min)`
        }
      }
    }

    return { isValid: true }
  }

  // 新的数据推送函数 - 按官方文档要求向所有匹配的订阅者推送数据
  function pushRealtimeDataToSubscribers(tickerData: any, klineTickerData: any) {
    if (!tickerData || !klineTickerData || !klineTickerData.currentPair || !klineTickerData.currentPeriod) {
      return
    }

    const currentSymbol = formatSymbol(klineTickerData.currentPair)
    const currentInterval = klineTickerData.currentPeriod
    const symbolKey = `${currentSymbol}_${currentInterval}`

    const validation = validateKlineData(klineTickerData, symbolKey)
    updateDataQualityStats(symbolKey, validation.isValid, validation.reason)

    if (!validation.isValid) {
      const last = (tickerData[currentSymbol] || {}).last
      if (last) {
        const lastBar = lastBarData.get(symbolKey)
        if (lastBar) {
          const currentClose = safeNumber(last)
          const updatedBar = {
            ...lastBar,
            close: currentClose,
            high: Math.max(lastBar.high, currentClose),
            low: Math.min(lastBar.low, currentClose)
          }

          // 更新缓存
          lastBarData.set(symbolKey, updatedBar)

          // 推送价格更新到订阅者
          const subscriberIds = symbolSubscriptions.get(symbolKey)
          if (subscriberIds && subscriberIds.size > 0) {
            subscriberIds.forEach(subscriberUID => {
              const subscription = subscriptions.get(subscriberUID)
              if (subscription && subscription.active) {
                if (formatSymbol(subscription.symbol) === currentSymbol &&
                  subscription.interval === currentInterval) {
                  try {
                    const barDataCopy = { ...updatedBar }
                    subscription.callback(barDataCopy)
                  } catch (error) {
                  }
                }
              }
            })
          }
        }
      }
      return
    }

    // 获取当前symbol和interval对应的所有订阅者
    const subscriberIds = symbolSubscriptions.get(symbolKey)
    if (!subscriberIds || subscriberIds.size === 0) {
      return
    }

    const last = (tickerData[currentSymbol] || {}).last
    if (!last) {
      return
    }

    // 构建K线数据
    const barData = buildBarData(klineTickerData, last, symbolKey)
    if (!barData) {

      // 即使时间无效，如果有最后的有效bar，仍然更新价格
      const lastValidBar = lastBarData.get(symbolKey)
      if (lastValidBar && last) {
        const priceOnlyUpdate = {
          ...lastValidBar,
          close: safeNumber(last),
          high: Math.max(lastValidBar.high, safeNumber(last)),
          low: Math.min(lastValidBar.low, safeNumber(last))
        }

        // 推送仅价格更新的数据
        subscriberIds.forEach(subscriberUID => {
          const subscription = subscriptions.get(subscriberUID)
          if (subscription && subscription.active &&
            formatSymbol(subscription.symbol) === currentSymbol &&
            subscription.interval === currentInterval) {
            try {
              subscription.callback({ ...priceOnlyUpdate })
            } catch (error) {
            }
          }
        })
      }
      return
    }

    // 向所有匹配的活跃订阅者推送数据 - 每个订阅者独立处理
    let pushCount = 0
    const callbackPromises = []

    subscriberIds.forEach(subscriberUID => {
      const subscription = subscriptions.get(subscriberUID)
      if (subscription && subscription.active) {
        // 验证数据匹配性（防止symbol/resolution不匹配的错误）
        if (formatSymbol(subscription.symbol) === currentSymbol &&
          subscription.interval === currentInterval) {

          const callbackPromise = new Promise((resolve) => {
            try {
              const barDataCopy = { ...barData }
              subscription.callback(barDataCopy)
              pushCount++
              resolve(true)
            } catch (error) {
              resolve(false)
            }
          })

          callbackPromises.push(callbackPromise)
        }
      }
    })

    // 等待所有回调完成
    if (callbackPromises.length > 0) {
      Promise.all(callbackPromises).then(() => {
        if (pushCount > 0) {
        }
      })
    }
  }

  // 存储每个订阅的最后一个bar时间，用于确保时间顺序正确
  const lastBarTime = new Map() // symbolKey -> timestamp
  const lastBarData = new Map() // symbolKey -> bar data

  // 数据源质量监控
  const dataQualityStats = new Map() // symbolKey -> { validCount, invalidCount, lastUpdate }

  function updateDataQualityStats(symbolKey: string, isValid: boolean, reason?: string) {
    if (!dataQualityStats.has(symbolKey)) {
      dataQualityStats.set(symbolKey, { validCount: 0, invalidCount: 0, lastUpdate: Date.now(), issues: [] })
    }

    const stats = dataQualityStats.get(symbolKey)
    if (isValid) {
      stats.validCount++
    } else {
      stats.invalidCount++
      if (reason && !stats.issues.includes(reason)) {
        stats.issues.push(reason)
        // 只保留最近10个问题类型
        if (stats.issues.length > 10) {
          stats.issues.shift()
        }
      }
    }
    stats.lastUpdate = Date.now()
  }

  // 根据官方文档：只能更新最后一个bar或添加新bar，不能修改历史数据
  function canUpdateBar(barTime: number, symbolKey: string): { canUpdate: boolean, isNewBar: boolean } {
    const lastTime = lastBarTime.get(symbolKey) || 0

    // 如果没有历史记录，允许任何时间
    if (lastTime === 0) {
      return { canUpdate: true, isNewBar: true }
    }

    // 判断是否是日线或更高周期
    const isDailyOrAbove = symbolKey.includes('_1d') || symbolKey.includes('_1D') ||
      symbolKey.includes('_3D') || symbolKey.includes('_1W') ||
      symbolKey.includes('_1M')

    // 计算时间差
    const timeDiff = barTime - lastTime
    const minuteDiff = timeDiff / 60000 // 转换为分钟
    const hourDiff = timeDiff / 3600000 // 转换为小时
    const dayDiff = timeDiff / 86400000 // 转换为天

    if (timeDiff > 0) {
      // 新的bar时间，可以添加
      return { canUpdate: true, isNewBar: true }
    } else if (timeDiff === 0) {
      // 相同时间，可以更新最后一个bar
      return { canUpdate: true, isNewBar: false }
    } else if (isDailyOrAbove && Math.abs(dayDiff) <= 1) {
      // 日线级别，允许1天内的时间差异
      return { canUpdate: true, isNewBar: false }
    } else if (!isDailyOrAbove && Math.abs(minuteDiff) <= 5) {
      // 分钟级别，允许5分钟内的时间差异
      return { canUpdate: true, isNewBar: false }
    } else {
      // 时间差太大，不能更新
      return { canUpdate: false, isNewBar: false }
    }
  }

  function buildBarData(val2: any, last: any, symbolKey: string) {
    const currentClose = safeNumber(last)

    // 优先使用完整的K线数据
    if (val2.time && val2.open !== undefined) {
      const barTime = Number(val2.time)
      const lastTime = lastBarTime.get(symbolKey) || 0

      // 强化时间验证：检查数据是否来自过去
      const now = Date.now()
      const maxAllowedPastTime = now - (7 * 24 * 60 * 60 * 1000) // 7天前

      if (barTime < maxAllowedPastTime) {
        // 使用最后有效bar更新价格
        const lastBar = lastBarData.get(symbolKey)
        if (lastBar) {
          return {
            ...lastBar,
            close: currentClose,
            high: Math.max(lastBar.high, currentClose),
            low: Math.min(lastBar.low, currentClose)
          }
        }
        return null
      }

      // 检查是否可以更新这个bar
      const { canUpdate, isNewBar } = canUpdateBar(barTime, symbolKey)

      if (!canUpdate) {
        // 策略：使用新价格更新最后一个有效bar
        const lastBar = lastBarData.get(symbolKey)
        if (lastBar) {
          const updatedBar = {
            ...lastBar,
            close: currentClose,
            high: Math.max(lastBar.high, currentClose),
            low: Math.min(lastBar.low, currentClose),
            // 如果新数据有volume，也可以更新volume
            volume: val2.volume !== undefined ? safeNumber(val2.volume) : lastBar.volume
          }

          // 更新缓存但保持时间不变
          lastBarData.set(symbolKey, updatedBar)

          return updatedBar
        }
        return null // 无法构建有效的bar
      }

      const barData = {
        time: barTime,
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: currentClose,
        volume: safeNumber(val2.volume)
      }

      // 更新记录
      lastBarTime.set(symbolKey, barTime)
      lastBarData.set(symbolKey, barData)

      // 更新lastCompleteBar缓存
      lastCompleteBar.value[symbolKey] = {
        open: barData.open,
        high: barData.high,
        low: barData.low,
        volume: barData.volume
      }

      return barData
    }

    // 对于只有价格变化的实时更新，更新最后一个bar
    const lastBar = lastBarData.get(symbolKey)
    if (lastBar) {
      // 根据官方文档：只能更新最后一个bar的价格，不能改变时间
      const updatedBar = {
        ...lastBar,
        close: currentClose,
        high: Math.max(lastBar.high, currentClose),
        low: Math.min(lastBar.low, currentClose)
      }

      // 更新缓存但不改变时间记录
      lastBarData.set(symbolKey, updatedBar)

      return updatedBar
    }

    const currentTime = Date.now()
    const resolution = symbolKey.split('_')[1]
    const resolvedResolution = ResolutionManager.getResolution(resolution) || '5'
    const currentBarStartTime = getCurrentBarStartTime(resolvedResolution, currentTime)
    
    const newBar = {
      time: currentBarStartTime,
      open: currentClose,
      high: currentClose,
      low: currentClose,
      close: currentClose,
      volume: 0
    }

    lastBarTime.set(symbolKey, currentBarStartTime)
    lastBarData.set(symbolKey, newBar)

    return newBar
  }

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    // 验证参数
    if (!symbolInfo) {
      return
    }

    // 兼容处理：优先使用 full_name，其次 fullName，最后 name
    const symbolName = symbolInfo.full_name || symbolInfo.fullName || symbolInfo.name || symbolInfo.symbol
    if (!symbolName || typeof symbolName !== 'string') {
      return
    }

    const symbolKey = `${symbolName}_${ResolutionManager.getInterval(resolution)}`
    // 创建订阅信息 - 按官方文档要求独立管理每个订阅者
    const subscription = {
      subscriberUID,
      symbol: symbolName,
      resolution,
      interval: ResolutionManager.getInterval(resolution),
      callback: onRealtimeCallback,
      onResetCacheNeededCallback,
      active: true,
      createdAt: Date.now()
    }

    // 存储订阅信息
    subscriptions.set(subscriberUID, subscription)

    // 按symbol_resolution分组管理订阅者
    if (!symbolSubscriptions.has(symbolKey)) {
      symbolSubscriptions.set(symbolKey, new Set())
    }
    symbolSubscriptions.get(symbolKey).add(subscriberUID)

    // 保留月度订阅缓存逻辑（用于1M特殊处理）
    if (ResolutionManager.getInterval(resolution) === '1M') {
      monthlySubscriptionCache = {
        key: symbolKey,
        subscription: subscription,
        pair: symbolName
      }
    }
    
    // 立即推送当前K线数据（如果有）
    const cachedBar = lastBarData.get(symbolKey)
    if (cachedBar) {
      setTimeout(() => {
        try {
          onRealtimeCallback({ ...cachedBar })
        } catch (error) {
        }
      }, 0)
    }
  }

  const datafeedAPI = {
    historyCallback: () => { },
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ],
        exchanges: [],
        symbols_types: [],
        supports_search: false,
        supports_group_request: false,
        supports_marks: false,
        supports_timescale_marks: false,
        supports_time: true,
        debug: false
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any, onResolveErrorCallback: any) {
      try {
        // 确保 symbolName 是字符串
        if (!symbolName || typeof symbolName !== 'string') {
          onResolveErrorCallback('Invalid symbol')
          return
        }

        let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
        pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue

        // 格式化交易对显示名称：SWAP合约显示为BTCUSDT格式，现货显示为BTC/USDT格式
        const formattedSymbolName = symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/')

        const symbolInfo = {
          symbol: formattedSymbolName,
          name: formattedSymbolName,
          ticker: formattedSymbolName,
          full_name: formattedSymbolName, // 添加 full_name 以兼容新版本
          description: formattedSymbolName, // 修正拼写错误: discription -> description
          exchange: 'KTX',
          listed_exchange: 'KTX',
          type: 'crypto', // 使用 'crypto' 而不是 'Spot'
          has_intraday: true,
          minmov: 1,
          minmove2: 0,
          pricescale: Math.pow(10, pricescaleValue),
          timezone: 'Etc/UTC',
          session: '0000-2400:2345671;1',
          volume_precision: 2,
          has_weekly_and_monthly: true,
          has_empty_bars: true,
          supported_resolutions: ['1', '3', '5', '15', '30', '60', '120', '240', '360', '480', '720', '1D', '3D', '1W', '1M'],
          data_status: 'streaming' // 添加数据状态
        }

        const timer = setTimeout(() => {
          onSymbolResolveCallback(symbolInfo)
          clearTimeout(timer)
        }, 0)
      } catch (error) {
        if (onResolveErrorCallback) {
          onResolveErrorCallback('Symbol resolution error')
        }
      }
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      // 获取订阅信息
      const subscription = subscriptions.get(subscriberUID)
      if (!subscription) {
        return
      }

      const symbolKey = `${subscription.symbol}_${subscription.interval}`

      // 标记订阅为非活跃状态（按官方文档，可能有延迟调用）
      subscription.active = false

      // 从symbol_resolution分组中移除
      const symbolSubs = symbolSubscriptions.get(symbolKey)
      if (symbolSubs) {
        symbolSubs.delete(subscriberUID)
        if (symbolSubs.size === 0) {
          // 没有订阅者了，清理这个symbolKey的相关数据
          symbolSubscriptions.delete(symbolKey)

          // 关键：清理该symbol+resolution的时间记录，避免后续订阅使用错误的时间
          lastBarTime.delete(symbolKey)
          lastBarData.delete(symbolKey)

          // 清理数据质量统计
          dataQualityStats.delete(symbolKey)
        }
      }

      // 移除订阅记录
      subscriptions.delete(subscriberUID)

      // 处理月度订阅缓存
      if (subscription.interval === '1M' &&
        monthlySubscriptionCache.subscription?.subscriberUID === subscriberUID) {
        monthlySubscriptionCache = {
          key: null,
          subscription: null,
          pair: null
        }
      }
    },
    clearCache,
    setForceRefresh,
    clearSymbolCache,
    setSymbolForceRefresh,
    // 新增调试和监控功能
    getSubscriptionStatus: () => {
      const status = {
        totalSubscriptions: subscriptions.size,
        symbolGroups: Object.fromEntries(symbolSubscriptions),
        activeSubscriptions: Array.from(subscriptions.entries()).map(([uid, sub]) => ({
          subscriberUID: uid,
          symbol: sub.symbol,
          interval: sub.interval,
          active: sub.active,
          createdAt: new Date(sub.createdAt).toISOString()
        }))
      }
      return status
    },
    forceReset: () => {
      subscriptions.clear()
      symbolSubscriptions.clear()
      lastCompleteBar.value = {}
      lastBarTime.clear()
      lastBarData.clear()
      pendingRequests.clear()
      activeRequests.clear()
      lastPriceUpdateTime.clear() // 清理价格更新时间记录
      
      // 清理ticker监听器防止内存泄漏
      if (tickerWatcher) {
        tickerWatcher() // 调用停止函数
        tickerWatcher = null
      }
      
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    },
    manualTriggerUpdate: () => {
      if (window.updateKlineDataTimestamp) {
        window.updateKlineDataTimestamp()
      }
    },
    resetTimeSequence,
    // 调试函数：检查当前的时间状态
    debugTimeState: () => {
      const timeState = Object.fromEntries(lastBarTime)
      const barDataState = Object.fromEntries(lastBarData)
      const barState = lastCompleteBar.value
      return { timeState, barDataState, barState }
    },
    // 数据质量报告
    getDataQualityReport: () => {
      const report = Object.fromEntries(
        Array.from(dataQualityStats.entries()).map(([key, stats]) => [
          key,
          {
            ...stats,
            successRate: stats.validCount / (stats.validCount + stats.invalidCount),
            lastUpdateTime: new Date(stats.lastUpdate).toISOString()
          }
        ])
      )
      return report
    },
    // 重置数据质量统计
    resetDataQualityStats: () => {
      dataQualityStats.clear()
    },
    
    // 新增：清理方法
    cleanup: () => {
      // 清理所有订阅
      subscriptions.clear()
      symbolSubscriptions.clear()
      lastBarsCache.clear()
      
      // 清理缓存
      dataCache.clear()
      pendingRequests.clear()
      activeRequests.clear()
      
      // 清理watcher
      if (tickerWatcher && typeof tickerWatcher === 'function') {
        tickerWatcher()
        tickerWatcher = null
      }
      
      // 清理RAF
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }
      
      // 清理数据质量统计
      dataQualityStats.clear()
    },
    
    // 改进forceReset方法
    forceReset: () => {
      // 清理订阅和缓存
      subscriptions.clear()
      symbolSubscriptions.clear()
      lastBarsCache.clear()
      dataCache.clear()
      pendingRequests.clear()
      activeRequests.clear()
      
      // 重置时间序列
      lastBarTime.clear()
      lastBarData.clear()
      
      // 清理数据质量统计
      dataQualityStats.clear()
      
      console.log('[Datafeed] Force reset completed')
    }
  }

  // 保存到Map中（使用新的实例管理）
  globalDatafeedInstances.set(instanceKey, datafeedAPI)

  // 确保window对象可以访问（用于调试）
  if (typeof window !== 'undefined') {
    window.datafeed = datafeedAPI
  }

  return datafeedAPI
}

// 导出重置函数，允许强制创建新实例
export function resetDatafeedInstance() {
  cleanupAllInstances()
  instanceCount = 0
}