<template>
  <Header modeType="future" />
  <Message  />
  <div class="exchange-container">
    <div class="exchange-wrapper future">
      <div name="left" class="cont-wrap-bg left"></div>
      <div name="right" class="cont-wrap-bg right"></div>
      <div name="subHeader" class="cont-wrap-bg subHeader">
        <FutureSubHeader
          :pair="pair"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          :ticker="tickers[pair]"
          :futuresType="futuresType"
          :pairInfo="pairInfo"
          :coinInfo="coinInfo"
          @changePair="changePair" />
      </div>
      <div name="trades" class="cont-wrap-bg trades">
        <FutureTrades
          :pair="pair"
          :futuresType="futuresType"
          :ticker="tickers[pair]"
          :deals="Object.values(deals[pair] || {})"
          :isCBCUnitUSD="isCBCUnitUSD"
          :isCBUUnitUSDT="isCBUUnitUSDT"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
           @change-price="changePrice"
          @change-amount="changeAmount"
        />
      </div>
      <div v-if="landingPairs[pair]" name="chart" class="cont-wrap-bg chart">
        <ExchangeLandingPair :data="landingPairs[pair]" :coinInfo="coinInfo" @timerEnd="timerEnd" />
      </div>
      <div v-else name="chart" class="cont-wrap-bg chart">
        <ClientOnly>
          <ExchangeCharts
            :pair="pair" 
            :propsDepth="depths"
            :futuresType="futuresType"
            :isCBCUnitUSD="isCBCUnitUSD"
            :isCBUUnitUSDT="isCBUUnitUSDT"
            :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
            :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
            :pair-amount-scales="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
            :deals="Object.values(deals[pair] || {})"
            :isFuture="true"
            :ticker="tickers[pair]"
            :coinInfo="coinInfo"
            @change-price="changePrice"
            @change-amount="changeAmount"
            @changePeriod="changePeriod"
          />
        </ClientOnly>
      </div>
      <div name="orderform" class="cont-wrap-bg orderform">
        <FutureOrderForm
          :pair="pair"
          :ticker="tickers[pair]"
          :futuresType="futuresType"
          :isCBCUnitUSD="isCBCUnitUSD"
          :isCBUUnitUSDT="isCBUUnitUSDT"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          :tradeAssetObj="tradeAssetObj"
          :posMapObj="posMapObj"
          :coinInfo="coinInfo"
          :amount="amount"
          :price="price"
          :tradeArr="tradeArr"
          :isLogin="isLogin"
          :pairInfo="pairInfo" />
          <!-- @updateAssets="getAssetsByCoin()" -->
      </div>
      <div name="orders" class="cont-wrap-bg orders">
        <FutureOrders
          :isLogin="isLogin"
          futuresType="lpc"
          :pair="pair"
          :amount="amount"
          :price="price"
          :coinInfo="coinInfo"
          :isCBCUnitUSD="isCBCUnitUSD"
          :isCBUUnitUSDT="isCBUUnitUSDT"
          :ticker="tickers[pair]"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          :tradeAssetObj="tradeAssetObj"
          :tradeArr="tradeArr"
          @changePair="changePair"
         />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { cookies } from '~/utils/cookies'
  import Message from '~/components/message/index.vue'
  import ExchangeCharts from '~/components/exchange/ExchangeCharts.vue'
  import ExchangeLandingPair from '~/components/exchange/ExchangeLandingPair.vue'
  import FutureSubHeader from '~/components/future/SubHeader.vue'
  import FutureTrades from '~/components/future/Trades.vue'
  import FutureOrderForm from '~/components/future/OrderForm.vue'
  import FutureOrders from '~/components/future/Orders.vue'
  import { getCoinParams } from '~/api/public'
  import { commonStore } from '~/stores/commonStore'
  import { useUserStore } from '~/stores/useUserStore'
  import { futureStore } from '~/stores/futureStore'
  const router = useRouter()
  const store = futureStore()
  const { subTicker, subDepth, setCbcTokenUnit, setCbuTokenUnit, getCBUPositions, getUserRate, subFutureInfoSocket } = store
  const { leverage, cbcTokenUnit, cbuTokenUnit, cbuPositions } = storeToRefs(store)
  const userStore = useUserStore()
  const { isLogin, userInfo } = storeToRefs(userStore)
  const publicStore = commonStore()
  const { getAssetsByCoin, cancelSocket, getDepthSocket, subTradesSocket, cancelKline, subTickerSocket, subLogin, getLandingPairs, subCOLLATERALTickerSocket, cancelCOLLATERALTickerSocket, reConnectUser, changePair, clearPairKlineListeners, clearAllKlineListeners } = publicStore
  const { tradeAssetObj, tradeArr, posMapObj, depthsStore, dealsObj, ticker, landingPairs, pairInfo, pair } = storeToRefs(publicStore)
  const { locale, t } = useI18n()
  // const pair = ref('')
  const oldPair = ref('')
  const depths = ref({})
  const deals = ref({})
  const tickers = ref({})
  const amount = ref('')
  const price = ref('')
  const requestAnimationFrameInterval = ref(null)
  const timerEnd = () => {
    getLandingPairs()
  }
  const coinInfo = ref({})
  const safeJsonParse = (jsonStr) => {
    // 1. 处理已解析的情况和空值
    if (jsonStr === null || jsonStr === undefined) {
      throw new Error('输入值为null或undefined');
    }
    
    if (typeof jsonStr === 'object') {
      return jsonStr;
    }

    // 2. 类型检查
    if (typeof jsonStr !== 'string') {
      try {
        return JSON.parse(String(jsonStr));
      } catch (e) {
        throw new Error(`非字符串输入转换失败: ${e.message}`);
      }
    }

    // 3. 预处理：仅做最必要的处理
    const preprocess = (str) => {
      return str.trim()
        // 移除BOM头
        .replace(/^\uFEFF/, '')
        // 标准化换行符
        .replace(/\r\n/g, '\n');
    };

    // 4. 尝试直接解析（首要尝试）
    try {
      return JSON.parse(jsonStr);
    } catch (initialError) {
      // 5. 仅做最小预处理后重试
      try {
        const cleaned = preprocess(jsonStr);
        return JSON.parse(cleaned);
      } catch (finalError) {
        // 6. 最终错误处理
        // console.error('JSON解析失败:', {
        //   original: jsonStr,
        //   error: finalError.message
        // });
        
        // 对于已知正确的JSON仍失败的情况，尝试最后的手段
        try {
          // 移除所有控制字符（保留换行符）
          const sanitized = jsonStr.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g, '');
          return JSON.parse(sanitized);
        } catch (lastResortError) {
          // throw new Error(`无法解析的JSON: ${finalError.message}`);
        }
      }
    }
  };
  const getCoinIfon = async() => {
    coinInfo.value = {}
    const { data } = await getCoinParams({
      coin_symbol: pair.value.split('_')[0]
    })
    if (data) {
      const depInfo = {}
      const lang = {
        'zh-cn': 'zh',
        'en-ww': 'en'
      }
      coinInfo.value = data
      if (data.describe_summary !== '') {
        try {
          safeJsonParse(String(data.describe_summary).trim()).forEach((item) => {
            depInfo[lang[item.lang]] = item.text
          })
          data['decription'] = depInfo
        } catch (e) {
          // console.error('解析失败:', e.message);
          // 处理错误情况
        }
      }
    }
  }
  const futuresType = computed(() => {
    if (pair.value.includes('_USDT') && pair.value.includes('_SWAP')) {
      return 'lpc' // U本位合约
    } else {
      return 'ipc' // 币本位合约
    }
  })
  const isCBCUnitUSD = computed(() => {
    return cbcTokenUnit.value === 'USD' && futuresType.value === 'ipc'
  })
  const isCBUUnitUSDT = computed(() => {
    return cbuTokenUnit.value === 'USDT' && futuresType.value === 'lpc'
  })
  const sub = (p) => {
    if (oldPair.value) {
      cancelSocket(oldPair.value); // 取消旧交易对的订阅
    }
    subFutureInfoSocket(p); // 订阅新交易对的合约信息
    subTickerSocket(p); // 订阅新交易对的行情数据
    subTradesSocket(p); // 订阅新交易对的交易数据
    getDepthSocket(p); // 订阅新交易对的深度数据
  }
  watch(() => pair.value, (nv, ov) => {
    console.log(nv, ov, 'ddkdjejdijeijijeijeij')
    oldPair.value = ov ? ov : pair.value
    if (ov) {
      cancelSocket(ov); // 取消旧交易对的订阅
      clearPairKlineListeners(ov) // 清理K线订阅
    }
    nextTick(() => {
      sub(nv) // 订阅新交易对的数据
      getCoinIfon() // 获取新交易对的币种信息
    })
  })
  watch(() => userInfo.value, (val) => {
    if (JSON.stringify(val) !== '{}') {
      getAssetsByCoin()
      subLogin()
      subCOLLATERALTickerSocket()
      getUserRate()
    }
  }, {
    immediate: true
  })
  const changeAmount = (amountP) => {
    amount.value = amountP
  }
  const changePrice = (priceP) => {
    price.value = Number(priceP).toFixed((pairInfo.value[pair.value] || {}).price_scale)
  }
  // const changePair = async(p) => {
  //   if (p.includes('_SWAP')) {
  //     pair.value = p; // 直接更新 pair 的值
  //     nextTick(() => {
  //       window.history.replaceState({}, null, `/${locale.value}/future/${p}`)
  //     })
  //     router.currentRoute.value.params.pair = p;
  //   } else {
  //     router.push(`/${locale.value}/exchange/${p}`)
  //   }
  // }
  const socketDateAnimation = () => {
    deals.value = dealsObj.value
    depths.value = depthsStore.value
    tickers.value = ticker.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const oldPeriod = ref('')
  const curPeriod = ref('')
  const lastShowTime = ref(0)
  const changePeriod = (Period) => {
    oldPeriod.value = curPeriod.value
    curPeriod.value = Period
  }
  const onVisibilityChange = () => {
    const isHidden = document.isHidden
    if (!isHidden && isLogin.value && new Date().getTime() - lastShowTime.value >= 1000 * 60) {
      reConnectUser(pair.value)
    } else {
      lastShowTime.value = new Date().getTime()
    }
  }
  onMounted(() => {
    document.addEventListener('visibilitychange', onVisibilityChange)
    setCbcTokenUnit(cookies.get(`futuresipcValuationUnit`) || 'USD')
    setCbuTokenUnit(cookies.get(`futureslpcValuationUnit`) || 'coinSymbol')
  })
  onBeforeMount(() => {
    const routePair = router.currentRoute.value.params.pair
    pair.value = routePair
    oldPair.value = routePair
    // 如果 URL 和实际不一致，强制修正
    if (!window.location.pathname.includes(routePair)) {
      window.history.replaceState({}, '', `/${locale.value}/future/${routePair}`);
    }
    sub(pair.value)
    getLandingPairs()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    clearPairKlineListeners(pair.value) // 使用新的清理函数
    cancelSocket(pair.value)
    cancelCOLLATERALTickerSocket()
    window.removeEventListener('visibilitychange', onVisibilityChange)
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
    
    // 清理TradingView实例（如果存在）
    import('~/composables/useDatafeedAction').then(module => {
      if (module.resetDatafeedInstance) {
        module.resetDatafeedInstance()
      }
    }).catch(() => {
      // 忽略导入错误
    })
  })
  useHead({
    title: () => `${tickers.value[pair.value]?.last || '--'} ${pair.value.replace('_SWAP', '').replace('_', '')} ${t('合约交易')}` || `${t('安全合规的数字资产交易平台,对meme币最友好的交易所')}`,
    titleTemplate: `${t('KTX')} | %s`,
    meta: [
      { hid: 'description', name: 'description', content: t('安全合规的数字资产交易平台,对meme币最友好的交易所') },
      { hid: 'keywords', name: 'keywords', content: t('KTX、区块链、数字货币、比特币、莱特币、以太坊，交易所，OSL') }
    ]
  });
</script>
<style lang="scss">
  @import url('@/assets/style/exchange/index.scss');
</style>
