import { defineStore } from 'pinia'
import { getErrors, listMainCoins, getPairList, getCurrencyRateData } from '~/api/public'
import { assetsByCoin, assetByCoinList } from '~/api/tf'
import { getTradesList, getKlinesApi } from '~/api/order'
import { getPairSetting, getLandingPairsApi, getAddDownLink } from '~/api/public'
import { useI18n } from "vue-i18n"
import { cookies } from '~/utils/cookies'
import { socket, socketAllTicker } from '~/utils'
import SocketClass from '~/utils/socket.ts'
import { nextTick } from 'vue'

let localPairInfo = {}
if (process.client) {
  localPairInfo = JSON.parse(localStorage.getItem('pairInfo') || '{}')
}

export const commonStore = defineStore('publicCommon', () => {
  // WebSocket监听器管理
  const klineListeners = new Map()
  const tickerListeners = new Map()
  const depthListeners = new Map()
  const tradesListeners = new Map()
  
  const errorMessages = ref({})
  const coinList = ref([])
  const currencyRate = ref({})
  const exchangeRate = ref({
    rate: 'USD',
    symbol: '$'
  })
  const pair = ref('')
  const depthsStore = ref({})
  const coinAssetListObj = ref({})
  const CoinAssetObj = ref({})
  const mainAssetObj = ref({})
  const tradeAssetObj = ref({})
  const posMapObj = ref({})
  const klineList = ref([])
  const klineTicker = ref({})
  const allPairList = ref([])
  const dealsObj = ref({})
  const allAsset = ref({})
  const marketsObj = ref({})
  const COLLATERALSymbol = ref({})
  const assetAllCoinMap = ref({})
  const ticker = ref({})
  const isChangeOrder = ref(true)
  const isChangeFutureOrder = ref(false)
  const isChangePosition = ref(false)
  const orderChangeObj = ref({})
  const isHideAssets = ref(false)
  const pairInfo = ref(localPairInfo)
  const priceScale = ref(0)
  const quantityScale = ref(0)
  const isPairDetail = ref(false)
  const landingPairs = ref({})
  const tradeArr = ref([])
  const downLoadInfo = ref({})
  const currentLang = useI18n().locale.value
  const changePair = (p) => {
    const router = useRouter()
    const nuxtApp = useNuxtApp()
    const lang = nuxtApp.$i18n.locale.value
    if (p.includes('_SWAP')) {
      pair.value = p
      nextTick(() => {
        window.history.replaceState({}, null, `/${lang}/future/${p}`)
      })
      router.currentRoute.value.params.pair = p
    } else {
      router.push(`/${lang}/exchange/${p}`)
    }
  }
  const getDownLoadInfo = async() => {
    const { data } = await getAddDownLink()
    if (data) {
      const arr = data.filter((item) => {
        return item.device_type * 1 === 3 || item.device_type * 1 === 4 // 3是IOS 4是安卓
      })
      let obj = {}
      arr.forEach((item) => {
        obj[item.device_type * 1] = item
      })
      downLoadInfo.value = obj
    }
  }
  const getLandingPairs = async() => {
    const { data } = await getLandingPairsApi()
    if (data) {
      let obj = {}
      data.forEach((item) => {
        obj[item.pair] = item
      })
      landingPairs.value = obj
    }
  }
  const setHideAssets = () => {
    isHideAssets.value = !isHideAssets.value
  }
  const getMessageError = async(lang) => {
    const { data } = await getErrors({
      lang: lang ? lang : currentLang
    })
    if (data) {
      errorMessages.value = data
    } else {
      try {
        let times = 0
        const timer = setInterval(async () => {
          if (times > 4) {
            clearInterval(timer)
            return
          }
          times++
          const {
            result: intervalData
          } = await getErrors({
            lang: currentLang
          })

          if (intervalData) {
            errorMessages.value = result
            clearInterval(timer)
          }
        }, 1000)
      } catch (err) {}
    }
  }
  const getCurrencyRate = async() => {
    const { data } = await getCurrencyRateData()
    if (data) {
      currencyRate.value = data
    } else {
      try {
        window.isDispatchCurrencyRate = false
      } catch (err) {}
    }
  }
  const switchExchangeRate = (data) => {
    exchangeRate.value = data
  }
  const getCoinList = async() => {
    const { data } = await listMainCoins()
    if (data) {
      coinList.value = data
    }
  }
  const getAllPairList = async() => {
    const { data } = await getPairList()
    if (data) {
      allPairList.value = data.spot.concat(data.contract)
    }
  }
  const getAssetByCoinList = async() => {
    const { data } = await assetByCoinList()
    if (data) {
      assetAllCoinMap.value = data
    }
  }
  const getAssetsByCoin = async() => {
    const { data } = await assetsByCoin()
    if (data) {
      allAsset.value = {
        'all': data.eq,
        'main': data.mainEq,
        'trade': data.tradeEq,
        'unprofit': data.posmap['USDT'].unprofit,
        'assetmap': data.assetmap
      }
      if (data.arr.length > 0) {
        data.arr.forEach((item) => {
          CoinAssetObj.value[item.asset] = item
        })
      } else {
        CoinAssetObj.value = {}
      }
      const mainArray = data.main
      if (mainArray.length > 0) {
        mainAssetObj.value = {}
        mainArray.forEach((item) => {
          mainAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['main'] = mainArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        mainAssetObj.value = {}
        coinAssetListObj.value['main'] = [
          {
            icon_url: data.assetmap['USDT'].icon_url,
            asset: 'USDT',
            maxTransferOut: 0,
            asset_weight: 0,
            balance: 0,
            balanceUnify: 0,
            c: false,
            collateral: true,
            discount: 1,
            discountForFee: 1,
            discountForMargin: 1,
            eqbtc: 0,
            eqcny: 0,
            equsdt: 0,
            holds: 0,
            total: 0,
            usdtunify: 0,
            withdrawable: 0
          }
        ]
      }
      const tradeArray = data.trade
      if (tradeArray.length > 0) {
        tradeAssetObj.value = {}
        tradeArray.forEach((item) => {
          tradeAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['trade'] = tradeArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        tradeAssetObj.value = {}
        coinAssetListObj.value['trade'] = tradeArray
      }
      tradeArr.value = tradeArray
      posMapObj.value = data.posmap
    }
  }
  const getPairDetail = async(type, pair) => {
    const { data } = await getPairSetting({
      all_spot: 1,
      all_cnt: 1
    })
    if (data) {
      pairInfo.value = data.map((v) => {
        v.price_scale = typeof Number(v.price_scale) === 'number' && !Number.isNaN(Number(v.price_scale)) ? Number(v.price_scale) : 2
        v.quantity_scale = typeof Number(v.quantity_scale) === 'number' && !Number.isNaN(Number(v.quantity_scale)) ? Number(v.quantity_scale) : 4
        return v
      }).reduce((acc, item) => {
        acc[item.symbol] = item
        return acc
      }, {})

      if (process.client) {
        localStorage.setItem('pairInfo', JSON.stringify(pairInfo.value))
      }
    }
  }
  const getDepthSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    const cb = (res) => {
      const convertToNumber = (value) => {
        return typeof value === 'string' ? parseFloat(value) : value;
      }
      if (res.t === 0) { // 全量
        const {
          asks,
          bids
        } = res.d
        const result = {
          pair: res.d.pair,
          asks,
          bids
        }
        depthsStore.value = { [pair]: result }
      } else {
        const {
          add,
          del
        } = res.d
        if (add.asks && depthsStore.value[pair]) {
          add.asks.forEach((v) => {
            depthsStore.value[pair].asks[v.price] = v
          })
        }
        if (add.bids && depthsStore.value[pair]) {
          add.bids.forEach((v) => {
            depthsStore.value[pair].bids[v.price] = v
          })
        }
        if (del.asks && depthsStore.value[pair]) {
          del.asks.forEach((v) => {
            if (Object.values(depthsStore.value[pair].asks).length > 0) {
              delete depthsStore.value[pair].asks[v.price]
            }
          })
        }
        if (del.bids && depthsStore.value[pair]) {
          del.bids.forEach((v) => {
            if (Object.values(depthsStore.value[pair].bids).length > 0) {
              delete depthsStore.value[pair].bids[v.price]
            }
          })
        }
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`, cb)
  }
  const transChartData = (obj) => {
    const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    const finalItem = {
      time: Number(obj[0])
    }
    obj.forEach((v, i) => {
      finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
    })
    return finalItem
  }
  const getKlineList = async(pair: any, time: any) => {
    const { data } = await getKlinesApi({
      symbol: pair,
      market: pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: time,
      limit: 1000
    })
    if (data) {
      klineList.value = data.e.map((item: any) => transChartData(item))
    }
  }
  const getKlineSocket = async(pair: any, time: any) => {
    return new Promise((resolve: Function, reject: Function) => {
      const eventName = `${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`
      const subscriptionKey = `${pair}-${time}`
      
      // 清理旧的监听器
      if (klineListeners.has(subscriptionKey)) {
        const oldListener = klineListeners.get(subscriptionKey)
        socket.off(oldListener.eventName, oldListener.callback)
        klineListeners.delete(subscriptionKey)
      }
      
      // 清理同一对的其他周期订阅（确保一个交易对只有一个K线订阅）
      for (const [key, listenerInfo] of klineListeners.entries()) {
        if (key.startsWith(`${pair}-`) && key !== subscriptionKey) {
          socket.off(listenerInfo.eventName, listenerInfo.callback)
          klineListeners.delete(key)
        }
      }
      
      socket.send({"method":"SUBSCRIBE","params":[eventName]})
      
      const cb = (res) => {
        if (res.t === 0) {
          klineList.value = res.d
          if (res.d.length && time === '1M') {
            klineTicker.value = {
              ...res.d[res.d.length - 1],
              currentPair: res.stream.split('.')[1],
              currentPeriod: time,
            }
            resolve()
            return
          }
        }
        if (res.d.length) {
          klineTicker.value = {
            ...res.d[res.d.length - 1],
            currentPair: res.stream.split('.')[1],
            currentPeriod: time,
          }
        }
        resolve()
      }
      
      // 注册新的监听器并记录
      socket.on(eventName, cb)
      klineListeners.set(subscriptionKey, {
        eventName,
        callback: cb,
        pair,
        time
      })
      
      // 添加超时处理，避免无限等待
      setTimeout(() => {
        resolve()
      }, 5000)
    })
  }
  const socketLogin = ref(null)
  const subLogin = () => {
    if (socketLogin.value && socketLogin.value.websocket) {
      socketLogin.value.destroy(); // 使用新增的销毁方法
      socketLogin.value = null;
    }
    socketLogin.value = new SocketClass(`wss://madex-user.tonetou.com`)
    const session_id = cookies.get('session_id_origin') || cookies.get('session_id')
    socketLogin.value.send({"method":"LOGIN","auth":{"sid": session_id}})
    const cb = (res) => { // 资产变化
      getAssetsByCoin()
    }
    const cbO = (res) => { // 订单变化
      if (res && res.data && res.data.product.includes('_SWAP')) {
        isChangeFutureOrder.value = true
      } else {
        isChangeOrder.value = true
      }
    }
    const cb1 = (res) => { // 仓位变化
      isChangePosition.value = true
    }
    socketLogin.value.on('account', cb)
    socketLogin.value.on('order', cbO)
    socketLogin.value.on('position', cb1)
  }
  const reConnectUser = (pair) => {
    if (pair.includes('_SWAP')) {
      isChangePosition.value = true
      isChangeFutureOrder.value = true
    } else {
      isChangeOrder.value = true
    }
    getAssetsByCoin()
  }
  const subTradesSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
    const cb = (res) => {
      if (res.t === 0) {
        dealsObj.value[pair] = res.d
        // 全量数据时，从最新成交记录中提取价格并同步更新ticker
        if (res.d && Object.keys(res.d).length > 0) {
          const trades = Object.values(res.d)
          if (trades.length > 0) {
            // 修复：确保reduce的初始值存在，并且正确比较时间戳
            const latestTrade = trades.reduce((latest, current) => {
              // 安全检查：确保时间戳存在且为数字
              const latestTime = latest && latest.t ? Number(latest.t) : 0
              const currentTime = current && current.t ? Number(current.t) : 0
              return currentTime > latestTime ? current : latest
            }, trades[0]) // 使用第一个元素作为初始值
            
            if (latestTrade && latestTrade.p) {
              syncPriceFromTrade(pair, latestTrade)
            }
          }
        }
      } else {
        // 增量数据处理 - 确保deals对象存在
        if (!dealsObj.value[pair]) {
          dealsObj.value[pair] = {}
        }
        
        Object.values(res.d).forEach((item) => {
          dealsObj.value[pair][item.i] = item
          // 增量数据时，立即从最新成交同步价格到ticker
          syncPriceFromTrade(pair, item)
        })
      }
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`, cb)
  }
  
  // 新增：从交易数据同步价格到ticker的函数
  const syncPriceFromTrade = (pair: string, tradeItem: any) => {
    try {
      if (!tradeItem || !tradeItem.p) return
      
      // 确保ticker对象存在
      if (!ticker.value[pair]) {
        ticker.value[pair] = {}
      }
      
      const newPrice = Number(tradeItem.p)
      const currentTime = Date.now()
      
      // 避免无效价格
      if (isNaN(newPrice) || newPrice <= 0) return
      
      // 创建price同步记录，用于防止数据冲突
      const syncInfo = {
        price: newPrice,
        timestamp: currentTime,
        source: 'trade'
      }
      
      // 立即更新ticker的last价格
      const currentTicker = ticker.value[pair]
      const oldPrice = currentTicker.last
      
      ticker.value[pair] = {
        ...currentTicker,
        last: newPrice,
        // 保存同步信息，供ticker WebSocket回调判断是否需要覆盖
        _syncInfo: syncInfo
      }
      
      // 可选：触发价格变化事件通知其他组件
      if (oldPrice !== newPrice) {
        // 可以在这里添加价格变化的事件通知
        console.debug(`[PriceSync] ${pair} 价格从 ${oldPrice} 更新到 ${newPrice}`)
      }
      
    } catch (error) {
      console.warn('[PriceSync] 价格同步失败:', error)
    }
  }
  const subTickerSocket = (pair: any) => {
    socket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    const cb = (res) => {
      const pairKey = res.data.product
      const incomingData = res.data
      const currentTicker = ticker.value[pairKey]
      
      // 数据一致性保护：检查是否应该保留trade同步的价格
      if (currentTicker && currentTicker._syncInfo) {
        const syncInfo = currentTicker._syncInfo
        const timeDiff = Date.now() - syncInfo.timestamp
        
        // 如果trade同步的价格是在最近3秒内且来源是trade，则保持trade的价格
        if (timeDiff < 3000 && syncInfo.source === 'trade') {
          // 保留trade同步的last价格，但更新其他ticker数据
          ticker.value[pairKey] = {
            ...incomingData,
            last: syncInfo.price, // 保持trade同步的价格
            _syncInfo: {
              ...syncInfo,
              tickerReceived: true // 标记已收到ticker更新
            }
          }
          console.debug(`[PriceSync] ${pairKey} 保持trade同步价格 ${syncInfo.price}，忽略ticker价格 ${incomingData.last}`)
          return
        } else {
          // 超过3秒或其他情况，清除同步信息，使用ticker数据
          // 修复：使用Vue响应式安全的方式更新对象
          const { _syncInfo, ...cleanTicker } = currentTicker
          ticker.value[pairKey] = { ...cleanTicker, ...incomingData }
          return
        }
      }
      
      // 正常情况：直接使用ticker数据
      ticker.value[pairKey] = incomingData
    }
    socket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`, cb)
  }
  const subAllTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["ALL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        marketsObj.value[item.product] = item
      })
    }
    socketAllTicker.on('ALL.ticker', cb)
  }
  const subCOLLATERALTickerSocket = () => {
    socketAllTicker.send({"method":"SUBSCRIBE","params":["COLLATERAL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        COLLATERALSymbol.value[item.symbol] = item
      })
    }
    socketAllTicker.on('COLLATERAL.ticker', cb)
  }
  const cancelCOLLATERALTickerSocket = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["COLLATERAL.ticker"]})
  }
  const cancelAllTicker = () => {
    socket.send({"method":"UNSUBSCRIBE","params":["ALL.ticker"]})
  }
  const cancelKline = (pair: any, time: any) => {
    const eventName = `${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`
    const subscriptionKey = `${pair}-${time}`
    
    // 发送取消订阅指令
    socket.send({"method":"UNSUBSCRIBE", "params":[eventName]})
    
    // 清理监听器
    if (klineListeners.has(subscriptionKey)) {
      const listenerInfo = klineListeners.get(subscriptionKey)
      socket.off(listenerInfo.eventName, listenerInfo.callback)
      klineListeners.delete(subscriptionKey)
    }
    
    // 清理数据状态（防止数据残留）
    if (klineTicker.value.currentPair === pair && klineTicker.value.currentPeriod === time) {
      klineTicker.value = {}
      klineList.value = []
    }
  }
  // 清理指定交易对的所有K线监听器
  const clearPairKlineListeners = (pair: any) => {
    const keysToDelete = []
    for (const [key, listenerInfo] of klineListeners.entries()) {
      if (key.startsWith(`${pair}-`)) {
        socket.off(listenerInfo.eventName, listenerInfo.callback)
        socket.send({"method":"UNSUBSCRIBE", "params":[listenerInfo.eventName]})
        keysToDelete.push(key)
      }
    }
    keysToDelete.forEach(key => klineListeners.delete(key))
    
    // 清理相关数据
    if (klineTicker.value.currentPair === pair) {
      klineTicker.value = {}
      klineList.value = []
    }
  }
  
  // 清理所有K线监听器（用于页面切换或重置）
  const clearAllKlineListeners = () => {
    for (const [key, listenerInfo] of klineListeners.entries()) {
      socket.off(listenerInfo.eventName, listenerInfo.callback)
      socket.send({"method":"UNSUBSCRIBE", "params":[listenerInfo.eventName]})
    }
    klineListeners.clear()
    klineTicker.value = {}
    klineList.value = []
  }
  
  const cancelSocket = (pair: any) => {
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    socket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
    
    // 同时清理该交易对的K线监听器
    clearPairKlineListeners(pair)
  }
  return {
    assetAllCoinMap,
    allAsset,
    coinAssetListObj,
    CoinAssetObj,
    mainAssetObj,
    tradeAssetObj,
    posMapObj,
    errorMessages,
    currencyRate,
    exchangeRate,
    coinList,
    depthsStore,
    ticker,
    klineList,
    klineTicker,
    allPairList,
    dealsObj,
    isChangeFutureOrder,
    isChangeOrder,
    isChangePosition,
    orderChangeObj,
    isHideAssets,
    marketsObj,
    pairInfo,
    priceScale,
    quantityScale,
    isPairDetail,
    landingPairs,
    tradeArr,
    COLLATERALSymbol,
    downLoadInfo,
    pair,
    subCOLLATERALTickerSocket,
    cancelCOLLATERALTickerSocket,
    getLandingPairs,
    getPairDetail,
    setHideAssets,
    switchExchangeRate,
    getCurrencyRate,
    getAssetByCoinList,
    getAllPairList,
    cancelKline,
    getKlineSocket,
    clearPairKlineListeners,
    clearAllKlineListeners,
    getMessageError,
    getCoinList,
    getAssetsByCoin,
    getDepthSocket,
    subLogin,
    subAllTickerSocket,
    cancelAllTicker,
    subTradesSocket,
    subTickerSocket,
    cancelSocket,
    getDownLoadInfo,
    reConnectUser,
    changePair,
    // 新增：价格同步相关的调试函数
    syncPriceFromTrade,
    // 调试函数：获取当前价格同步状态
    getPriceSyncStatus: () => {
      const syncStatus = {}
      Object.keys(ticker.value).forEach(pair => {
        const tickerData = ticker.value[pair]
        if (tickerData._syncInfo) {
          syncStatus[pair] = {
            price: tickerData.last,
            syncInfo: tickerData._syncInfo,
            timeDiff: Date.now() - tickerData._syncInfo.timestamp
          }
        }
      })
      return syncStatus
    }
  }
})